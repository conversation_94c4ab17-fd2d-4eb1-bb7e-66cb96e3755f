# CattePM SAR图像降噪滤波器使用指南

## 概述

我已经为您创建了完整的CattePM滤波器代码，用于对配准后的SAR图像进行降噪处理。该实现包含了方差σ（滤波尺度）、扩散程度常数λ和迭代次数n作为输入参数，降噪后的图像将保存到cattePM文件夹中。

## 新增文件列表

### 1. 核心文件
- **`cattepm_filter.py`** - CattePM滤波器主要实现
- **`main_with_cattepm.py`** - 集成CattePM的完整处理流程
- **`run_cattepm_example.py`** - 使用示例脚本
- **`test_cattepm.py`** - 测试脚本

### 2. 文档文件
- **`README_CattePM.md`** - 详细技术文档
- **`CattePM_使用指南.md`** - 本使用指南

### 3. 修改的文件
- **`utils.py`** - 添加了cattePM文件夹支持

## 快速开始

### 方法1: 使用示例脚本（推荐）

```bash
# 确保已有配准后的图像在registered文件夹中
python run_cattepm_example.py
```

### 方法2: 直接使用CattePM滤波器

```bash
# 处理registered文件夹中的所有图像
python cattepm_filter.py

# 使用自定义参数
python cattepm_filter.py --sigma 1.5 --lambda_val 0.1 --iterations 25

# 处理单个图像
python cattepm_filter.py --single_image registered/reg_frame_0001.png
```

### 方法3: 使用集成的完整流程

```bash
# 运行包含CattePM的完整处理流程
python main_with_cattepm.py --video 3.mp4 --cattepm_sigma 2.0 --cattepm_lambda 0.125 --cattepm_iterations 30
```

## CattePM参数说明

### 核心参数
- **`sigma` (σ)**: 方差，控制滤波尺度
  - 默认值: 2.0
  - 推荐范围: 0.5 - 5.0
  - 较小值保留更多细节，较大值平滑效果更强

- **`lambda_val` (λ)**: 扩散程度常数
  - 默认值: 0.125
  - 推荐范围: 0.01 - 0.5
  - 控制扩散速度，较小值扩散更慢更稳定

- **`iterations` (n)**: 迭代次数
  - 默认值: 30
  - 推荐范围: 10 - 100
  - 更多迭代通常产生更好的降噪效果

## 使用场景

### 场景1: 批量处理配准后的图像

```python
from cattepm_filter import process_registered_images

# 处理registered文件夹中的所有图像
process_registered_images(
    input_folder='registered',
    output_folder='cattePM',
    sigma=2.0,
    lambda_val=0.125,
    iterations=30
)
```

### 场景2: 处理单个图像

```python
from cattepm_filter import process_single_image

# 处理单个图像
process_single_image(
    image_path='registered/reg_frame_0001.png',
    sigma=2.0,
    lambda_val=0.125,
    iterations=30
)
```

### 场景3: 在代码中直接调用

```python
from cattepm_filter import cattepm_denoising
import cv2

# 读取图像
image = cv2.imread('registered/reg_frame_0001.png')

# 应用CattePM降噪
denoised = cattepm_denoising(image, sigma=2.0, lambda_val=0.125, iterations=30)

# 保存结果
cv2.imwrite('cattePM/denoised_result.png', denoised)
```

## 输出结果

- **输出文件夹**: `cattePM/`
- **文件命名**: `cattepm_frame_XXXX.png`
- **图像格式**: PNG（8位灰度或24位彩色）
- **像素值范围**: 0-255

## 性能优化建议

1. **GPU加速**: 确保安装CUDA版本的PyTorch以获得最佳性能
2. **批量处理**: 使用批量处理功能提高效率
3. **参数调优**: 根据图像特点调整参数
   - 高噪声图像: 增加迭代次数
   - 保留细节: 减小sigma值
   - 强平滑效果: 增大lambda_val值

## 测试和验证

### 运行测试脚本

```bash
python test_cattepm.py
```

测试脚本将：
- 创建测试图像
- 测试不同参数组合
- 评估性能
- 验证设备兼容性

### 检查结果质量

1. 查看`test_cattepm`文件夹中的参数对比结果
2. 比较原始图像和降噪后图像
3. 评估噪声抑制效果和边缘保持能力

## 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 使用CPU模式
   export CUDA_VISIBLE_DEVICES=""
   python cattepm_filter.py
   ```

2. **处理速度慢**
   - 检查PyTorch CUDA安装
   - 减少迭代次数
   - 使用较小的sigma值

3. **图像质量不佳**
   - 调整sigma参数（控制平滑程度）
   - 调整lambda_val参数（控制扩散速度）
   - 增加迭代次数

### 参数调优建议

| 图像特点 | sigma | lambda_val | iterations |
|---------|-------|------------|------------|
| 高噪声 | 2.5-3.0 | 0.15-0.2 | 40-50 |
| 中等噪声 | 2.0 | 0.125 | 30 |
| 低噪声 | 1.0-1.5 | 0.1 | 20-25 |
| 保留细节 | 1.0 | 0.05-0.1 | 20-30 |

## 集成到现有流程

CattePM滤波器已经集成到项目中：

1. **utils.py**: 添加了cattePM文件夹支持
2. **main_with_cattepm.py**: 完整的集成处理流程
3. **独立使用**: 可以单独使用cattepm_filter.py

## 下一步

1. 运行示例脚本验证功能
2. 根据您的SAR图像特点调整参数
3. 集成到您的完整处理流程中
4. 根据需要进行性能优化

## 技术支持

如果遇到问题，请：
1. 查看README_CattePM.md获取详细技术信息
2. 运行test_cattepm.py进行诊断
3. 检查CUDA和PyTorch安装

---

**注意**: 确保在运行CattePM滤波器之前，已经有配准后的图像在`registered`文件夹中。如果没有，请先运行主程序生成配准图像。

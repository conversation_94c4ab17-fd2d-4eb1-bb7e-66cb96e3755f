#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CattePM滤波器测试脚本
用于测试CattePM滤波器的功能和性能
"""

import cv2
import numpy as np
import os
import time
import torch
from cattepm_filter import cattepm_denoising, process_registered_images, process_single_image

def create_test_image():
    """
    创建一个测试用的SAR图像（模拟斑点噪声）
    """
    # 创建基础图像
    height, width = 256, 256
    image = np.zeros((height, width), dtype=np.float32)
    
    # 添加一些几何形状作为目标
    cv2.rectangle(image, (50, 50), (100, 100), 200, -1)  # 矩形目标
    cv2.circle(image, (180, 180), 30, 150, -1)           # 圆形目标
    
    # 添加背景
    image += 50
    
    # 添加SAR斑点噪声（乘性噪声）
    noise = np.random.gamma(1, 1, (height, width))
    image = image * noise
    
    # 归一化到0-255范围
    image = np.clip(image, 0, 255).astype(np.uint8)
    
    return image

def test_cattepm_parameters():
    """
    测试不同CattePM参数的效果
    """
    print("=== 测试CattePM参数效果 ===")
    
    # 创建测试图像
    test_image = create_test_image()
    
    # 创建测试目录
    test_dir = 'test_cattepm'
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # 保存原始图像
    cv2.imwrite(os.path.join(test_dir, 'original.png'), test_image)
    print("保存原始测试图像")
    
    # 测试不同参数组合
    test_params = [
        {'sigma': 1.0, 'lambda_val': 0.1, 'iterations': 20, 'name': 'low_sigma'},
        {'sigma': 2.0, 'lambda_val': 0.125, 'iterations': 30, 'name': 'default'},
        {'sigma': 3.0, 'lambda_val': 0.15, 'iterations': 40, 'name': 'high_sigma'},
        {'sigma': 2.0, 'lambda_val': 0.05, 'iterations': 30, 'name': 'low_lambda'},
        {'sigma': 2.0, 'lambda_val': 0.25, 'iterations': 30, 'name': 'high_lambda'},
    ]
    
    for params in test_params:
        print(f"测试参数: {params['name']} - σ={params['sigma']}, λ={params['lambda_val']}, n={params['iterations']}")
        
        start_time = time.time()
        denoised = cattepm_denoising(test_image, 
                                   sigma=params['sigma'],
                                   lambda_val=params['lambda_val'],
                                   iterations=params['iterations'])
        end_time = time.time()
        
        # 保存结果
        output_path = os.path.join(test_dir, f"cattepm_{params['name']}.png")
        cv2.imwrite(output_path, denoised)
        
        print(f"  处理时间: {end_time - start_time:.2f}秒")
        print(f"  保存至: {output_path}")
    
    print(f"参数测试完成，结果保存在 {test_dir} 文件夹中")

def test_performance():
    """
    测试CattePM滤波器的性能
    """
    print("\n=== 性能测试 ===")
    
    # 测试不同图像尺寸
    sizes = [(128, 128), (256, 256), (512, 512)]
    
    for width, height in sizes:
        print(f"测试图像尺寸: {width}x{height}")
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (height, width), dtype=np.uint8)
        
        # 测试处理时间
        start_time = time.time()
        denoised = cattepm_denoising(test_image, sigma=2.0, lambda_val=0.125, iterations=30)
        end_time = time.time()
        
        processing_time = end_time - start_time
        pixels_per_second = (width * height) / processing_time
        
        print(f"  处理时间: {processing_time:.2f}秒")
        print(f"  处理速度: {pixels_per_second:.0f} 像素/秒")

def test_device_compatibility():
    """
    测试设备兼容性（CPU/GPU）
    """
    print("\n=== 设备兼容性测试 ===")
    
    # 检查CUDA可用性
    if torch.cuda.is_available():
        print("CUDA可用")
        print(f"GPU设备: {torch.cuda.get_device_name()}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    else:
        print("CUDA不可用，使用CPU")
    
    # 创建测试图像
    test_image = create_test_image()
    
    # 测试处理
    start_time = time.time()
    denoised = cattepm_denoising(test_image)
    end_time = time.time()
    
    print(f"处理时间: {end_time - start_time:.2f}秒")
    print("设备兼容性测试通过")

def test_batch_processing():
    """
    测试批量处理功能
    """
    print("\n=== 批量处理测试 ===")
    
    # 创建测试输入目录
    test_input_dir = 'test_input'
    test_output_dir = 'test_output'
    
    if not os.path.exists(test_input_dir):
        os.makedirs(test_input_dir)
    
    # 创建多个测试图像
    num_images = 5
    for i in range(num_images):
        test_image = create_test_image()
        # 添加一些变化
        test_image = test_image + np.random.randint(-20, 20, test_image.shape)
        test_image = np.clip(test_image, 0, 255).astype(np.uint8)
        
        filename = f"test_frame_{i:04d}.png"
        cv2.imwrite(os.path.join(test_input_dir, filename), test_image)
    
    print(f"创建了 {num_images} 个测试图像")
    
    # 测试批量处理
    start_time = time.time()
    process_registered_images(input_folder=test_input_dir,
                            output_folder=test_output_dir,
                            sigma=2.0,
                            lambda_val=0.125,
                            iterations=20)
    end_time = time.time()
    
    print(f"批量处理时间: {end_time - start_time:.2f}秒")
    
    # 检查输出结果
    if os.path.exists(test_output_dir):
        output_files = os.listdir(test_output_dir)
        print(f"成功处理 {len(output_files)} 个图像")
    
    # 清理测试文件
    import shutil
    if os.path.exists(test_input_dir):
        shutil.rmtree(test_input_dir)
    if os.path.exists(test_output_dir):
        shutil.rmtree(test_output_dir)
    
    print("批量处理测试完成")

def main():
    """
    运行所有测试
    """
    print("CattePM滤波器测试开始")
    print("=" * 50)
    
    try:
        # 测试参数效果
        test_cattepm_parameters()
        
        # 测试性能
        test_performance()
        
        # 测试设备兼容性
        test_device_compatibility()
        
        # 测试批量处理
        test_batch_processing()
        
        print("\n" + "=" * 50)
        print("所有测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

# CattePM SAR图像降噪滤波器

## 概述

本项目实现了基于CattePM模型的SAR图像降噪滤波器，专门用于对配准后的SAR图像进行降噪处理。CattePM（Catte-Perona-Malik）模型是一种基于偏微分方程的图像降噪方法，特别适用于SAR图像的斑点噪声抑制。

## 主要特性

- **专门针对SAR图像优化**: 基于CattePM模型，有效抑制SAR图像中的斑点噪声
- **可调参数**: 支持调整方差σ（滤波尺度）、扩散程度常数λ和迭代次数n
- **批量处理**: 支持对整个文件夹中的图像进行批量降噪处理
- **单图像处理**: 支持对单个图像进行降噪处理
- **GPU加速**: 支持CUDA加速计算（如果可用）

## 文件结构

```
├── cattepm_filter.py          # CattePM滤波器主要实现
├── run_cattepm_example.py     # 使用示例脚本
├── README_CattePM.md          # 本说明文件
├── registered/                # 输入：配准后的SAR图像文件夹
└── cattePM/                   # 输出：降噪后的图像文件夹（自动创建）
```

## 安装依赖

确保安装了以下Python包：

```bash
pip install opencv-python numpy torch tqdm
```

## 使用方法

### 1. 批量处理配准后的图像

```python
from cattepm_filter import process_registered_images

# 使用默认参数处理registered文件夹中的所有图像
process_registered_images()

# 使用自定义参数
process_registered_images(
    input_folder='registered',
    output_folder='cattePM',
    sigma=2.0,          # 方差σ（滤波尺度）
    lambda_val=0.125,   # 扩散程度常数λ
    iterations=30       # 迭代次数n
)
```

### 2. 处理单个图像

```python
from cattepm_filter import process_single_image

# 处理单个图像
process_single_image(
    image_path='registered/reg_frame_0001.png',
    output_path='cattePM/cattepm_frame_0001.png',
    sigma=2.0,
    lambda_val=0.125,
    iterations=30
)
```

### 3. 命令行使用

```bash
# 处理registered文件夹中的所有图像（使用默认参数）
python cattepm_filter.py

# 使用自定义参数
python cattepm_filter.py --input registered --output cattePM --sigma 1.5 --lambda_val 0.1 --iterations 25

# 处理单个图像
python cattepm_filter.py --single_image registered/reg_frame_0001.png --sigma 2.0 --lambda_val 0.125 --iterations 30
```

### 4. 运行示例脚本

```bash
python run_cattepm_example.py
```

## 参数说明

### CattePM模型参数

- **sigma (σ)**: 方差，控制滤波尺度
  - 默认值: 2.0
  - 范围: 0.5 - 5.0
  - 较小值保留更多细节，较大值平滑效果更强

- **lambda_val (λ)**: 扩散程度常数
  - 默认值: 0.125
  - 范围: 0.01 - 0.5
  - 控制扩散速度，较小值扩散更慢更稳定

- **iterations (n)**: 迭代次数
  - 默认值: 30
  - 范围: 10 - 100
  - 更多迭代通常产生更好的降噪效果，但计算时间更长

## 算法原理

CattePM模型基于以下偏微分方程：

```
∂I/∂t = λ · c(|∇(G_σ * I)|) · ΔI
```

其中：
- `I` 是图像强度
- `G_σ` 是标准差为σ的高斯核
- `c(·)` 是扩散函数
- `Δ` 是拉普拉斯算子
- `λ` 是扩散程度常数

该模型通过在边缘处减少扩散、在平滑区域增加扩散来实现边缘保持的降噪效果。

## 输出结果

- 降噪后的图像保存在`cattePM`文件夹中
- 文件命名格式：`cattepm_frame_XXXX.png`
- 图像格式：PNG（8位灰度或24位彩色）
- 像素值范围：0-255

## 性能优化

- 自动检测并使用GPU加速（如果CUDA可用）
- 使用PyTorch进行高效的卷积运算
- 支持批量处理以提高效率

## 注意事项

1. 确保输入图像是配准后的SAR图像
2. 处理大量图像时建议使用GPU加速
3. 参数调整建议：
   - 对于高噪声图像，增加迭代次数
   - 对于需要保留细节的图像，减小sigma值
   - 对于需要更强平滑效果，增大lambda_val值

## 示例结果

处理前后的图像对比：
- 原始配准图像：保留了SAR图像的斑点噪声
- CattePM降噪后：有效抑制斑点噪声，同时保持边缘和目标特征

## 故障排除

1. **CUDA内存不足**: 减少批处理大小或使用CPU模式
2. **处理速度慢**: 检查是否正确安装了CUDA版本的PyTorch
3. **图像质量不佳**: 调整CattePM参数，特别是sigma和lambda_val

## 联系信息

如有问题或建议，请联系开发团队。

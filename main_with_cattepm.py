#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成CattePM滤波器的SAR视频处理主程序
在原有处理流程基础上，增加专门的CattePM降噪处理步骤
"""

import cv2
import numpy as np
import os
import argparse
from tqdm import tqdm
import torch
from registration import register_frames, visualize_registration
from denoising import adaptive_denoising
from cattepm_filter import cattepm_denoising, process_registered_images
from segmentation import segment_image
from utils import create_output_dirs, save_frame, draw_bounding_boxes
from post_processing import post_process
from connected_components import analyze_connected_components
from detection import detect_motion_targets

def process_video_with_cattepm(video_path, output_video_path=None,
                              # 原有参数
                              filter_type='cattepm', seg_method='otsu',
                              iterations=30, sigma_gaussian=2.0, K_diff=20.0, lambda_pm=0.125,
                              lk_window_size=7, lee_damping=1.0,
                              # CattePM专用参数
                              use_cattepm=True, cattepm_sigma=2.0, cattepm_lambda=0.125, cattepm_iterations=30,
                              # 动目标检测参数
                              min_displacement=15.0, min_trajectory_length=3, max_distance=40.0,
                              min_area_similarity=0.2, min_area_threshold=30):
    """
    处理SAR视频，包含CattePM专用降噪步骤
    
    参数:
        video_path: 输入视频路径
        output_video_path: 输出视频路径
        use_cattepm: 是否使用专门的CattePM降噪处理
        cattepm_sigma: CattePM方差σ（滤波尺度）
        cattepm_lambda: CattePM扩散程度常数λ
        cattepm_iterations: CattePM迭代次数n
        其他参数: 与原main.py相同
    """
    # 创建输出目录（包括cattePM文件夹）
    create_output_dirs()
    
    # 打开视频文件
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"无法打开视频: {video_path}")
        return
    
    # 获取视频属性
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"视频信息: {width}x{height}, {fps:.2f}fps, {frame_count}帧")
    
    # 设置输出视频
    if output_video_path is None:
        output_video_path = 'result_video_cattepm.mp4'
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_video_path, fourcc, fps, (width, height))
    
    # 读取第一帧
    ret, prev_frame = cap.read()
    if not ret:
        print("无法读取视频帧")
        return
    
    # 存储所有处理后的帧
    all_frames = [prev_frame]
    registered_frames = [prev_frame]  # 第一帧不需要配准
    
    # 对第一帧进行标准降噪处理
    denoised_frame = adaptive_denoising(prev_frame, filter_type=filter_type,
                                        sigma_gaussian=sigma_gaussian, K_diff=K_diff,
                                        lambda_val=lambda_pm, iterations=iterations,
                                        lee_kuan_window_size=lk_window_size,
                                        lee_damping_factor=lee_damping)
    denoised_frames = [denoised_frame]
    
    # 如果启用CattePM，对第一帧进行CattePM降噪
    if use_cattepm:
        cattepm_frame = cattepm_denoising(prev_frame, 
                                        sigma=cattepm_sigma,
                                        lambda_val=cattepm_lambda,
                                        iterations=cattepm_iterations)
        save_frame(cattepm_frame, 0, 'cattePM', 'cattepm_')
        cattepm_frames = [cattepm_frame]
    
    # 对降噪后的图像进行分割
    segmented_frame = segment_image(denoised_frame, method=seg_method)
    segmented_frames = [segmented_frame]
    
    # 后处理
    post_processed_frame = post_process(segmented_frame, kernel_size_open=(7, 7), kernel_size_close=(7, 7))
    post_processed_frames = [post_processed_frame]
    
    # 保存第一帧结果
    save_frame(prev_frame, 0, 'registered', 'reg_')
    save_frame(denoised_frame, 0, 'denoised', 'den_')
    save_frame(segmented_frame, 0, 'segmented', 'seg_')
    save_frame(post_processed_frame, 0, 'post_processed', 'post_')
    
    # 处理剩余帧
    frame_idx = 1
    pbar = tqdm(total=frame_count, desc="处理视频帧")
    
    while True:
        ret, current_frame = cap.read()
        if not ret:
            break
        
        # 图像配准
        registered_frame, _ = register_frames(prev_frame, current_frame)
        save_frame(registered_frame, frame_idx, 'registered', 'reg_')
        
        # 标准降噪处理
        denoised_frame = adaptive_denoising(registered_frame, filter_type=filter_type,
                                            sigma_gaussian=sigma_gaussian, K_diff=K_diff,
                                            lambda_val=lambda_pm, iterations=iterations,
                                            lee_kuan_window_size=lk_window_size,
                                            lee_damping_factor=lee_damping)
        save_frame(denoised_frame, frame_idx, 'denoised', 'den_')
        
        # CattePM专用降噪处理
        if use_cattepm:
            cattepm_frame = cattepm_denoising(registered_frame,
                                            sigma=cattepm_sigma,
                                            lambda_val=cattepm_lambda,
                                            iterations=cattepm_iterations)
            save_frame(cattepm_frame, frame_idx, 'cattePM', 'cattepm_')
            cattepm_frames.append(cattepm_frame)
        
        # 分割
        segmented_frame = segment_image(denoised_frame, method=seg_method)
        save_frame(segmented_frame, frame_idx, 'segmented', 'seg_')
        
        # 后处理
        post_processed_frame = post_process(segmented_frame, kernel_size_open=(7, 7), kernel_size_close=(7, 7))
        save_frame(post_processed_frame, frame_idx, 'post_processed', 'post_')
        
        # 存储处理后的帧
        all_frames.append(current_frame)
        registered_frames.append(registered_frame)
        denoised_frames.append(denoised_frame)
        segmented_frames.append(segmented_frame)
        post_processed_frames.append(post_processed_frame)
        
        # 更新前一帧
        prev_frame = registered_frame.copy()
        
        frame_idx += 1
        pbar.update(1)
    
    pbar.close()
    cap.release()
    
    # 如果使用了CattePM，显示统计信息
    if use_cattepm:
        print(f"\nCattePM降噪处理完成！")
        print(f"参数: σ={cattepm_sigma}, λ={cattepm_lambda}, 迭代次数={cattepm_iterations}")
        print(f"处理了 {len(cattepm_frames)} 帧图像")
        print(f"结果保存在 'cattePM' 文件夹中")
    
    # 执行动目标检测
    print("\n开始动目标检测...")
    motion_trajectories = detect_motion_targets(
        post_processed_folder="post_processed",
        original_frames_folder="registered",
        output_folder="detected",
        min_displacement=min_displacement,
        min_trajectory_length=min_trajectory_length,
        max_distance=max_distance,
        min_area_similarity=min_area_similarity,
        min_area_threshold=min_area_threshold
    )
    
    print(f"\n动目标检测完成！检测到 {len(motion_trajectories)} 个运动目标")
    return motion_trajectories

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='SAR视频动目标检测（集成CattePM滤波器）')
    parser.add_argument('--video', type=str, default='3.mp4', help='输入SAR视频路径')
    parser.add_argument('--output', type=str, default='result_video_cattepm.mp4', help='输出视频路径')
    
    # 标准降噪参数
    parser.add_argument('--filter', type=str, default='cattepm',
                        choices=['lee', 'kuan', 'cattepm'],
                        help='标准降噪滤波器类型')
    parser.add_argument('--iterations', type=int, default=10, help='标准滤波器迭代次数')
    parser.add_argument('--sigma_gaussian', type=float, default=2.0, help='标准CattePM: 高斯平滑sigma')
    parser.add_argument('--K_diff', type=float, default=10.0, help='标准CattePM: 扩散系数K值')
    parser.add_argument('--lambda_pm', type=float, default=0.125, help='标准CattePM: 扩散速率lambda')
    
    # CattePM专用参数
    parser.add_argument('--use_cattepm', action='store_true', default=True, 
                        help='是否使用专门的CattePM降噪处理')
    parser.add_argument('--cattepm_sigma', type=float, default=2.0, 
                        help='CattePM专用: 方差σ（滤波尺度）')
    parser.add_argument('--cattepm_lambda', type=float, default=0.125, 
                        help='CattePM专用: 扩散程度常数λ')
    parser.add_argument('--cattepm_iterations', type=int, default=30, 
                        help='CattePM专用: 迭代次数n')
    
    # 其他参数
    parser.add_argument('--lk_window_size', type=int, default=7, help='Lee/Kuan窗口大小')
    parser.add_argument('--lee_damping', type=float, default=1.0, help='Lee阻尼系数')
    parser.add_argument('--seg_method', type=str, default='otsu',
                        choices=['otsu', 'global', 'adaptive'], help='分割方法')
    
    # 动目标检测参数
    parser.add_argument('--min_displacement', type=float, default=50.0, help='最小位移阈值')
    parser.add_argument('--min_trajectory_length', type=int, default=8, help='最小轨迹长度')
    parser.add_argument('--max_distance', type=float, default=30.0, help='最大匹配距离')
    parser.add_argument('--min_area_similarity', type=float, default=0.2, help='最小面积相似度')
    parser.add_argument('--min_area_threshold', type=int, default=50, help='最小面积阈值')
    
    args = parser.parse_args()
    
    # 检查设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 确保窗口大小为奇数
    if args.lk_window_size % 2 == 0:
        args.lk_window_size += 1
        print(f"调整窗口大小为奇数: {args.lk_window_size}")
    
    # 处理视频
    process_video_with_cattepm(
        video_path=args.video,
        output_video_path=args.output,
        filter_type=args.filter,
        seg_method=args.seg_method,
        iterations=args.iterations,
        sigma_gaussian=args.sigma_gaussian,
        K_diff=args.K_diff,
        lambda_pm=args.lambda_pm,
        lk_window_size=args.lk_window_size,
        lee_damping=args.lee_damping,
        use_cattepm=args.use_cattepm,
        cattepm_sigma=args.cattepm_sigma,
        cattepm_lambda=args.cattepm_lambda,
        cattepm_iterations=args.cattepm_iterations,
        min_displacement=args.min_displacement,
        min_trajectory_length=args.min_trajectory_length,
        max_distance=args.max_distance,
        min_area_similarity=args.min_area_similarity,
        min_area_threshold=args.min_area_threshold
    )

if __name__ == "__main__":
    main()
